import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function Home() {
	return (
		<div className="min-h-screen flex items-center justify-center bg-background">
			<div className="text-center space-y-6">
				<h1 className="text-4xl font-bold text-foreground">
					Pukpara back on next.js
				</h1>
				<p className="text-lg text-muted-foreground">
					Building authentication frontend...
				</p>
				<div className="space-x-4">
					<Button asChild>
						<Link href="/sign-in">Go to Sign In</Link>
					</Button>
				</div>
			</div>
		</div>
	);
}
