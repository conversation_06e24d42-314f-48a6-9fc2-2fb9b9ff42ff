import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

interface CheckEmailProps {
	email: string;
}

export function CheckEmail({ email }: CheckEmailProps) {
	return (
		<Card className="border-0 shadow-none mt-2">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				<CardTitle className="text-lg font-semibold text-foreground leading-7">
					Check your email
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					Please click the link sent to your email
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<div className="space-y-4">
					{/* Email Display */}
					<div className="p-3 bg-muted rounded-md">
						<p className="text-sm font-medium text-foreground">{email}</p>
					</div>

					{/* Instructions */}
					<p className="text-sm text-muted-foreground">
						to verify to reset your password. Thank you
					</p>

					{/* Open Mail Button */}
					<Button
						className="w-full"
						size="lg"
						onClick={() => window.open("mailto:", "_blank")}
					>
						Open mail
					</Button>

					{/* Back to Sign In Link */}
					<div className="mt-6 text-center">
						<span className="text-sm text-muted-foreground">
							<Link
								href="/sign-in"
								className="text-primary hover:underline font-medium"
							>
								← Back to Sign In
							</Link>
						</span>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
