"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
	email: z.string().min(1, {
		message: "Email is required.",
	}),

	password: z
		.string()
		.min(1, {
			message: "Password is required.",
		})
		.min(4, {
			message: "Password must be at least 4 characters.",
		}),
	keepSignedIn: z.boolean(),
});

export function SignInForm() {
	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zod<PERSON><PERSON>olver(formSchema),
		defaultValues: {
			email: "",
			password: "",
			keepSignedIn: false,
		},
	});

	// 2. Define a submit handler.
	function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		console.log("Login form submitted:", values);
	}

	return (
		<Card className="border-0 shadow-none mt-2">
			<CardHeader className="space-y-0 text-left lg:text-left p-0">
				<CardTitle className="text-xl font-semibold text-foreground leading-7">
					Sign In
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5 ">
					Kindly enter the following details to login.
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						{/* Email Field */}
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Email
									</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Password Field */}
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Enter your password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Keep me signed in & Forgot password */}
						<div className="flex items-center justify-between">
							<FormField
								control={form.control}
								name="keepSignedIn"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-y-0">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className="leading-none">
											<FormLabel className="text-sm font-normal text-foreground">
												Keep me signed in
											</FormLabel>
										</div>
									</FormItem>
								)}
							/>
							<Link
								href="/forgot-password"
								className="text-sm text-primary hover:underline"
							>
								Forgot password?
							</Link>
						</div>

						{/* Sign In Button */}
						<Button type="submit" className="w-full" size="lg">
							Sign In
						</Button>
					</form>
				</Form>

				{/* Sign Up Link */}
				<div className="mt-6 text-center">
					<span className="text-sm text-muted-foreground">
						Not a member yet?{" "}
						<Link
							href="/register"
							className="text-primary hover:underline font-medium"
						>
							Sign up
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
