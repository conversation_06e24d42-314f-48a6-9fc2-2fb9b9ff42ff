"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";

const formSchema = z.object({
	agreeToTerms: z.boolean().refine((value) => value === true, {
		message: "You must agree to the terms and conditions.",
	}),
	agreeToPrivacy: z.boolean().refine((value) => value === true, {
		message: "You must agree to the privacy policy.",
	}),
	subscribeToNewsletter: z.boolean(),
});

export function SignUpStep3Form() {
	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zod<PERSON><PERSON>olver(formSchema),
		defaultValues: {
			agreeToTerms: false,
			agreeToPrivacy: false,
			subscribeToNewsletter: false,
		},
	});

	// 2. Define a submit handler.
	function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		console.log("Sign up step 3 form submitted:", values);
		// Navigate to dashboard or success page
		window.location.href = "/dashboard";
	}

	return (
		<Card className="border-0 shadow-none mt-6">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				{/* Step indicator */}
				<div className="text-sm text-primary font-medium mb-2">Step 3 of 3</div>
				<CardTitle className="text-lg font-semibold text-foreground leading-7">
					Almost there!
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					Review and accept our terms to complete your registration
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Terms and Conditions */}
						<FormField
							control={form.control}
							name="agreeToTerms"
							render={({ field }) => (
								<FormItem className="flex flex-row items-start space-x-3 space-y-0">
									<FormControl>
										<Checkbox
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
									<div className="space-y-1 leading-none">
										<FormLabel className="text-sm font-normal text-foreground">
											I agree to the{" "}
											<Link
												href="/terms"
												className="text-primary hover:underline"
											>
												Terms and Conditions
											</Link>
										</FormLabel>
										<FormMessage />
									</div>
								</FormItem>
							)}
						/>

						{/* Privacy Policy */}
						<FormField
							control={form.control}
							name="agreeToPrivacy"
							render={({ field }) => (
								<FormItem className="flex flex-row items-start space-x-3 space-y-0">
									<FormControl>
										<Checkbox
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
									<div className="space-y-1 leading-none">
										<FormLabel className="text-sm font-normal text-foreground">
											I agree to the{" "}
											<Link
												href="/privacy"
												className="text-primary hover:underline"
											>
												Privacy Policy
											</Link>
										</FormLabel>
										<FormMessage />
									</div>
								</FormItem>
							)}
						/>

						{/* Newsletter Subscription */}
						<FormField
							control={form.control}
							name="subscribeToNewsletter"
							render={({ field }) => (
								<FormItem className="flex flex-row items-start space-x-3 space-y-0">
									<FormControl>
										<Checkbox
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
									<div className="space-y-1 leading-none">
										<FormLabel className="text-sm font-normal text-foreground">
											Subscribe to our newsletter for updates and tips
										</FormLabel>
									</div>
								</FormItem>
							)}
						/>

						{/* Create Account Button */}
						<Button type="submit" className="w-full" size="lg">
							Create Account
						</Button>
					</form>
				</Form>

				{/* Back Link */}
				<div className="mt-6 text-center">
					<Link
						href="/sign-up/step-2"
						className="text-sm text-muted-foreground hover:text-primary"
					>
						← Back to previous step
					</Link>
				</div>
			</CardContent>
		</Card>
	);
}
