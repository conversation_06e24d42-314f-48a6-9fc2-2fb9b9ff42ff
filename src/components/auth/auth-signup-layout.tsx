import Image from "next/image";
import Link from "next/link";

interface AuthSignupLayoutProps {
	children: React.ReactNode;
}

export function AuthSignupLayout({ children }: AuthSignupLayoutProps) {
	return (
		<div className="min-h-screen flex flex-col lg:flex-row">
			{/* Left side - Form Content */}
			<div className="flex-1 flex flex-col justify-center p-4 sm:p-8 bg-background">
				<div className="w-full max-w-md mx-auto">
					{/* Logo - positioned to the left on web, centered on mobile */}
					<div className="flex justify-center lg:justify-start">
						<Image
							src="/pukpara-logo.png"
							alt="Pukpara Logo"
							width={194}
							height={55}
						/>
					</div>

					{/* Form Content */}
					{children}

					{/* Footer Links */}
					<div className="mt-8 flex justify-center space-x-6 text-sm text-primary">
						<Link href="/terms" className="hover:text-primary">
							Terms
						</Link>
						<Link href="/plans" className="hover:text-primary">
							Plans
						</Link>
						<Link href="/contact" className="hover:text-primary">
							Contact Us
						</Link>
					</div>
				</div>
			</div>

			{/* Right side - Image */}
			<div className="hidden lg:flex lg:flex-1 relative">
				<Image
					src="/auth-image2.jpg"
					alt="Authentication background"
					fill
					className="object-cover"
					priority
				/>
			</div>
		</div>
	);
}
