"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
	phoneNumber: z.string().min(1, {
		message: "Phone number is required.",
	}),
	country: z.string().min(1, {
		message: "Country is required.",
	}),
	district: z.string().min(1, {
		message: "District is required.",
	}),
	address: z.string().min(1, {
		message: "Address is required.",
	}),
});

export function SignUpStep2Form() {
	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zod<PERSON><PERSON>olver(formSchema),
		defaultValues: {
			phoneNumber: "",
			country: "",
			district: "",
			address: "",
		},
	});

	// 2. Define a submit handler.
	function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		console.log("Sign up step 2 form submitted:", values);
		// Navigate to step 3
		window.location.href = "/sign-up/step-3";
	}

	return (
		<Card className="border-0 shadow-none mt-6">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				{/* Step indicator */}
				<div className="text-sm text-primary font-medium mb-2">Step 2 of 3</div>
				<CardTitle className="text-lg font-semibold text-foreground leading-7">
					Where can we reach you?
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					Help us connect with you and customize your experience
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						{/* Phone Number Field */}
						<FormField
							control={form.control}
							name="phoneNumber"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Phone number
									</FormLabel>
									<FormControl>
										<Input
											type="tel"
											placeholder="+233 55 000 0000"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Country Field */}
						<FormField
							control={form.control}
							name="country"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Country
									</FormLabel>
									<FormControl>
										<Input placeholder="Ghana" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* District Field */}
						<FormField
							control={form.control}
							name="district"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										District
									</FormLabel>
									<FormControl>
										<Input placeholder="WS-443-2343" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Address Field */}
						<FormField
							control={form.control}
							name="address"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Address
									</FormLabel>
									<FormControl>
										<Input placeholder="WS-443-2343" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Continue Button */}
						<Button type="submit" className="w-full" size="lg">
							Continue
						</Button>
					</form>
				</Form>

				{/* Back Link */}
				<div className="mt-6 text-center">
					<Link
						href="/sign-up"
						className="text-sm text-muted-foreground hover:text-primary"
					>
						← Back to previous step
					</Link>
				</div>
			</CardContent>
		</Card>
	);
}
