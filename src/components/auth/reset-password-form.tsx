"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z
	.object({
		newPassword: z
			.string()
			.min(1, {
				message: "New password is required.",
			})
			.min(6, {
				message: "Password must be at least 8 characters.",
			}),
		confirmPassword: z.string().min(1, {
			message: "Please confirm your password.",
		}),
	})
	.refine((data) => data.newPassword === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

export function ResetPasswordForm() {
	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			newPassword: "",
			confirmPassword: "",
		},
	});

	// 2. Define a submit handler.
	function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		console.log("Reset password form submitted:", values);
	}

	return (
		<Card className="border-0 shadow-none mt-2">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				<CardTitle className="text-lg font-semibold text-foreground leading-7">
					Reset password
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					You're almost done! Choose a strong password for your account
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						{/* New Password Field */}
						<FormField
							control={form.control}
							name="newPassword"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										New password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Enter your new password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Confirm Password Field */}
						<FormField
							control={form.control}
							name="confirmPassword"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Confirm password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Confirm your new password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Reset Password Button */}
						<Button type="submit" className="w-full" size="lg">
							Reset password
						</Button>
					</form>
				</Form>

				{/* Back to Sign In Link */}
				<div className="mt-6 text-center">
					<span className="text-sm text-muted-foreground">
						Remember your password?{" "}
						<Link
							href="/sign-in"
							className="text-primary hover:underline font-medium"
						>
							Back to Sign In
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
