"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z
	.object({
		firstName: z.string().min(1, {
			message: "First name is required.",
		}),
		lastName: z.string().min(1, {
			message: "Last name is required.",
		}),
		email: z
			.string()
			.min(1, {
				message: "Email is required.",
			})
			.email({
				message: "Please enter a valid email address.",
			}),
		password: z
			.string()
			.min(1, {
				message: "Password is required.",
			})
			.min(8, {
				message: "Password must be at least 8 characters.",
			}),
		confirmPassword: z.string().min(1, {
			message: "Please confirm your password.",
		}),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

export function RegisterForm() {
	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			firstName: "",
			lastName: "",
			email: "",
			password: "",
			confirmPassword: "",
		},
	});

	// 2. Define a submit handler.
	function onSubmit(values: z.infer<typeof formSchema>) {
		// Do something with the form values.
		// ✅ This will be type-safe and validated.
		console.log("Register form submitted:", values);
		// Navigate to step 2
		window.location.href = "/sign-up/step-2";
	}

	return (
		<Card className="border-0 shadow-none mt-6">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				{/* Step indicator */}
				<div className="text-sm text-primary font-medium mb-2">Step 1 of 3</div>
				<CardTitle className="text-lg font-semibold text-foreground leading-7">
					Let's create your account
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					Set up your basic credentials to get started
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						{/* Name Fields */}
						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="firstName"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-foreground">
											First name
										</FormLabel>
										<FormControl>
											<Input placeholder="John" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="lastName"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-foreground">
											Last name
										</FormLabel>
										<FormControl>
											<Input placeholder="Doe" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Email Field */}
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Email
									</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Password Field */}
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Enter your password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Confirm Password Field */}
						<FormField
							control={form.control}
							name="confirmPassword"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Confirm password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Confirm your password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Continue Button */}
						<Button type="submit" className="w-full" size="lg">
							Continue
						</Button>
					</form>
				</Form>

				{/* Sign In Link */}
				<div className="mt-6 text-center">
					<span className="text-sm text-muted-foreground">
						Already have an account?{" "}
						<Link
							href="/sign-in"
							className="text-primary hover:underline font-medium"
						>
							Sign In
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
